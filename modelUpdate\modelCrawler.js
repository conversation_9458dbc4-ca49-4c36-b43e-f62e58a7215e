/**
 * OpenRouter Model Crawler
 * Fetches and processes models from OpenRouter API
 */

const fs = require('fs');
const path = require('path');

class ModelCrawler {
  constructor() {
    this.apiUrl = 'https://openrouter.ai/api/v1/models';
    this.outputPath = path.join(__dirname, 'models-manifest.json');
  }

  // Flagship model criteria - latest and largest from major providers
  getFlagshipCriteria() {
    return {
      'openai': {
        patterns: ['gpt-4o', 'o1-preview', 'o1-mini', 'chatgpt-4o-latest'],
        exclude: ['turbo-preview', '0314', '0613', '1106-preview']
      },
      'anthropic': {
        patterns: ['claude-3.5-sonnet', 'claude-3-opus', 'claude-opus-4', 'claude-sonnet-4'],
        exclude: ['claude-2.1', 'claude-2.0', 'claude-3-haiku', 'claude-instant']
      },
      'google': {
        patterns: ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-pro-1.5'],
        exclude: ['gemini-1.0', 'gemma']
      },
      'x-ai': {
        patterns: ['grok-4', 'grok-3', 'grok-2'],
        exclude: []
      },
      'meta-llama': {
        patterns: ['llama-3.3-70b', 'llama-3.1-405b', 'llama-3.1-70b'],
        exclude: ['llama-2', 'llama-3-8b']
      },
      'deepseek': {
        patterns: ['deepseek-r1', 'deepseek-v3'],
        exclude: ['deepseek-coder', 'deepseek-math']
      },
      'qwen': {
        patterns: ['qwen-2.5-72b', 'qwen-2.5-coder', 'qwen-max'],
        exclude: ['qwen-1.5', 'qwen-7b']
      }
    };
  }

  // Determine if a model is flagship using creation date + version number
  isFlagshipModel(model) {
    const modelId = model.id.toLowerCase();
    const modelName = model.name.toLowerCase();

    // Anthropic Claude models - use version number and creation date
    if (modelId.includes('anthropic') && modelId.includes('claude')) {
      // Claude 4 models (created Feb 2025+) are flagship
      if (modelId.includes('claude-opus-4') || modelId.includes('claude-sonnet-4')) {
        return true;
      }

      // Claude 3.5 Sonnet and Claude 3 Opus are flagship
      if (modelId.includes('claude-3.5-sonnet') || modelId.includes('claude-3-opus')) {
        return true;
      }

      // Exclude older versions
      if (modelId.includes('claude-2') || modelId.includes('claude-instant') ||
          modelId.includes('claude-3-haiku')) {
        return false;
      }
    }

    // Use traditional pattern matching for other providers
    const criteria = this.getFlagshipCriteria();
    for (const [provider, config] of Object.entries(criteria)) {
      if (provider === 'anthropic') continue; // Already handled above

      if (modelId.includes(provider) || modelName.includes(provider)) {
        const matchesPattern = config.patterns.some(pattern =>
          modelId.includes(pattern.toLowerCase()) || modelName.includes(pattern.toLowerCase())
        );

        const isExcluded = config.exclude.some(exclude =>
          modelId.includes(exclude.toLowerCase()) || modelName.includes(exclude.toLowerCase())
        );

        return matchesPattern && !isExcluded;
      }
    }

    return false;
  }

  // Categorize models dynamically
  categorizeModel(model) {
    const modelId = model.id.toLowerCase();
    const modelName = model.name.toLowerCase();
    const description = (model.description || '').toLowerCase();
    
    const categories = [];
    
    // Free models
    if (model.pricing.prompt === "0" && model.pricing.completion === "0") {
      categories.push('free');
    }
    
    // Flagship models
    if (this.isFlagshipModel(model)) {
      categories.push('flagship');
    }
    
    // Reasoning models
    if (modelId.includes('o1-') || modelId.includes('reasoning') || 
        modelName.includes('thinking') || modelName.includes('reasoning') ||
        description.includes('reasoning') || description.includes('chain-of-thought')) {
      categories.push('reasoning');
    }
    
    // Vision models
    if (model.architecture.modality.includes('image') ||
        modelId.includes('vision') || modelName.includes('vision') ||
        description.includes('vision') || description.includes('multimodal')) {
      categories.push('vision');
    }
    
    // Code models
    if (modelId.includes('code') || modelId.includes('devstral') ||
        modelName.includes('code') || modelName.includes('dev') ||
        description.includes('coding') || description.includes('software')) {
      categories.push('code');
    }
    
    return categories;
  }

  // Extract provider from model ID
  getProvider(model) {
    const parts = model.id.split('/');
    return parts[0] || 'unknown';
  }

  // Process and enrich model data
  processModel(model) {
    return {
      id: model.id,
      name: model.name,
      description: model.description || '',
      context_length: model.context_length,
      pricing: {
        prompt: model.pricing.prompt,
        completion: model.pricing.completion
      },
      top_provider: {
        max_completion_tokens: model.top_provider.max_completion_tokens
      },
      architecture: {
        modality: model.architecture.modality,
        tokenizer: model.architecture.tokenizer,
        instruct_type: model.architecture.instruct_type
      },
      per_request_limits: model.per_request_limits,
      // Enhanced metadata
      provider: this.getProvider(model),
      categories: this.categorizeModel(model),
      is_flagship: this.isFlagshipModel(model),
      is_free: model.pricing.prompt === "0" && model.pricing.completion === "0",
      created_timestamp: model.created,
      supported_parameters: model.supported_parameters || [],
      // Update tracking
      last_updated: new Date().toISOString(),
      crawl_version: "1.0"
    };
  }

  // Fetch models from OpenRouter API
  async fetchModels() {
    try {
      console.log('Fetching models from OpenRouter API...');
      
      const response = await fetch(this.apiUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching models:', error);
      throw error;
    }
  }

  // Generate model manifest
  async generateManifest() {
    try {
      const rawModels = await this.fetchModels();
      console.log(`Fetched ${rawModels.length} raw models`);
      
      // Process and enrich models
      const processedModels = rawModels.map(model => this.processModel(model));
      
      // Sort by flagship first, then by name
      processedModels.sort((a, b) => {
        if (a.is_flagship && !b.is_flagship) return -1;
        if (!a.is_flagship && b.is_flagship) return 1;
        return a.name.localeCompare(b.name);
      });
      
      // Generate statistics
      const stats = {
        total_models: processedModels.length,
        flagship_models: processedModels.filter(m => m.is_flagship).length,
        free_models: processedModels.filter(m => m.is_free).length,
        vision_models: processedModels.filter(m => m.categories.includes('vision')).length,
        reasoning_models: processedModels.filter(m => m.categories.includes('reasoning')).length,
        code_models: processedModels.filter(m => m.categories.includes('code')).length,
        providers: [...new Set(processedModels.map(m => m.provider))].length
      };
      
      // Create manifest
      const manifest = {
        version: new Date().toISOString().split('T')[0].replace(/-/g, '.'), // YYYY.MM.DD
        last_updated: new Date().toISOString(),
        crawl_timestamp: Date.now(),
        statistics: stats,
        models: processedModels,
        featured_models: processedModels
          .filter(m => m.is_flagship)
          .slice(0, 10)
          .map(m => m.id),
        deprecated_models: [], // Can be manually maintained
        metadata: {
          source: 'OpenRouter API',
          crawler_version: '1.0',
          total_processed: processedModels.length
        }
      };
      
      return manifest;
    } catch (error) {
      console.error('Error generating manifest:', error);
      throw error;
    }
  }

  // Save manifest to file
  async saveManifest(manifest) {
    try {
      const jsonString = JSON.stringify(manifest, null, 2);
      fs.writeFileSync(this.outputPath, jsonString, 'utf8');
      console.log(`Manifest saved to: ${this.outputPath}`);
      console.log(`Total models: ${manifest.models.length}`);
      console.log(`Flagship models: ${manifest.statistics.flagship_models}`);
      console.log(`Free models: ${manifest.statistics.free_models}`);
    } catch (error) {
      console.error('Error saving manifest:', error);
      throw error;
    }
  }

  // Main crawl function
  async crawl() {
    try {
      console.log('Starting model crawl...');
      const manifest = await this.generateManifest();
      await this.saveManifest(manifest);
      console.log('Model crawl completed successfully!');
      return manifest;
    } catch (error) {
      console.error('Model crawl failed:', error);
      throw error;
    }
  }
}

// Export for use as module
module.exports = ModelCrawler;

// Run if called directly
if (require.main === module) {
  const crawler = new ModelCrawler();
  crawler.crawl().catch(console.error);
}
