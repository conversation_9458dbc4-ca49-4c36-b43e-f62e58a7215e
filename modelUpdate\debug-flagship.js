/**
 * Debug Flagship Detection
 * Test the flagship detection logic for Claude 4 models
 */

const ModelCrawler = require('./modelCrawler.js');

// Test models
const testModels = [
  {
    id: "anthropic/claude-opus-4",
    name: "Anthropic: Claude Opus 4",
    description: "Claude Opus 4 is benchmarked as the world's best coding model...",
    pricing: { prompt: "0.000015", completion: "0.000075" }
  },
  {
    id: "anthropic/claude-sonnet-4", 
    name: "Anthropic: Claude Sonnet 4",
    description: "Claude Sonnet 4 significantly enhances the capabilities...",
    pricing: { prompt: "0.000003", completion: "0.000015" }
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Anthropic: Claude 3.5 Sonnet", 
    description: "Claude 3.5 Sonnet delivers better-than-Opus capabilities...",
    pricing: { prompt: "0.000003", completion: "0.000015" }
  },
  {
    id: "anthropic/claude-3-opus",
    name: "Anthropic: Claude 3 Opus",
    description: "Claude 3 Opus is Anthropic's most powerful model...",
    pricing: { prompt: "0.000015", completion: "0.000075" }
  }
];

function debugFlagshipDetection() {
  console.log('🔍 Debugging Flagship Detection for Claude Models\n');
  
  const crawler = new ModelCrawler();
  const criteria = crawler.getFlagshipCriteria();
  
  console.log('📋 Current Anthropic Flagship Criteria:');
  console.log('   Patterns:', criteria.anthropic.patterns);
  console.log('   Exclude:', criteria.anthropic.exclude);
  console.log('');
  
  testModels.forEach(model => {
    console.log(`🧪 Testing: ${model.name}`);
    console.log(`   ID: ${model.id}`);
    
    const isFlagship = crawler.isFlagshipModel(model);
    console.log(`   Is Flagship: ${isFlagship ? '✅ YES' : '❌ NO'}`);
    
    // Debug the matching process
    const modelId = model.id.toLowerCase();
    const modelName = model.name.toLowerCase();
    
    console.log(`   Model ID (lower): ${modelId}`);
    console.log(`   Model Name (lower): ${modelName}`);
    
    // Check provider match
    const providerMatch = modelId.includes('anthropic') || modelName.includes('anthropic');
    console.log(`   Provider Match: ${providerMatch ? '✅' : '❌'}`);
    
    if (providerMatch) {
      // Check pattern matches
      const patternMatches = criteria.anthropic.patterns.map(pattern => {
        const idMatch = modelId.includes(pattern.toLowerCase());
        const nameMatch = modelName.includes(pattern.toLowerCase());
        return {
          pattern,
          idMatch,
          nameMatch,
          matches: idMatch || nameMatch
        };
      });
      
      console.log('   Pattern Matches:');
      patternMatches.forEach(pm => {
        console.log(`     "${pm.pattern}": ID=${pm.idMatch ? '✅' : '❌'}, Name=${pm.nameMatch ? '✅' : '❌'}, Overall=${pm.matches ? '✅' : '❌'}`);
      });
      
      const anyPatternMatch = patternMatches.some(pm => pm.matches);
      console.log(`   Any Pattern Match: ${anyPatternMatch ? '✅' : '❌'}`);
      
      // Check exclusions
      const exclusionMatches = criteria.anthropic.exclude.map(exclude => {
        const idMatch = modelId.includes(exclude.toLowerCase());
        const nameMatch = modelName.includes(exclude.toLowerCase());
        return {
          exclude,
          idMatch,
          nameMatch,
          matches: idMatch || nameMatch
        };
      });
      
      console.log('   Exclusion Matches:');
      exclusionMatches.forEach(em => {
        console.log(`     "${em.exclude}": ID=${em.idMatch ? '❌' : '✅'}, Name=${em.nameMatch ? '❌' : '✅'}, Overall=${em.matches ? '❌ EXCLUDED' : '✅'}`);
      });
      
      const anyExclusionMatch = exclusionMatches.some(em => em.matches);
      console.log(`   Any Exclusion Match: ${anyExclusionMatch ? '❌ EXCLUDED' : '✅'}`);
      
      console.log(`   Final Result: ${anyPatternMatch && !anyExclusionMatch ? '✅ FLAGSHIP' : '❌ NOT FLAGSHIP'}`);
    }
    
    console.log('');
  });
}

debugFlagshipDetection();
